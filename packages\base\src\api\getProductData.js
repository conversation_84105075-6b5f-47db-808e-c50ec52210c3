// 获取产品相关的数据的共用接口
import axios from '@fin-tec/utils/src/axios';
import { adapterVersion } from '@fin-tec/form-generator/src/components/FormDesign/RightPanel/FormItemConfig/ItemConfig/versionStatus';

const URL = {
	PRODUCT_VERSION_LIST: '/manage/product/getVersionList'
};

/**
 * @description 根据产品类型去查询产品的版本列表
 * @param {产品类型} productTypeCode
 * @param {是否根据登录人的权限查询} isAll
 * @returns Array
 */
const getProductVersionList = (productTypeCode, isAll = true) => {
	return axios
		.get(URL.PRODUCT_VERSION_LIST, {
			params: { productTypeCode, isAll }
		})
		.then(data => {
			if (data.code === 0) {
				return data.data;
			}
		});
};
const adaptProductList = function (productList = []) {
	for (let i = 0; i < productList.length; i++) {
		productList[i].children = productList[i].item;
		productList[i].label = productList[i].name;
		productList[i].value = productList[i].id;
		for (let j = 0; j < productList[i].children.length; j++) {
			productList[i].children[j].label = productList[i].children[j].versionNo;
			productList[i].children[j].value = productList[i].children[j].versionId;
		}
	}
	return adapterVersion(productList);
};
export default {
	getProductVersionList,
	adaptProductList
};
