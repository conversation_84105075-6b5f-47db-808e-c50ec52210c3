import axios from '@fin-tec/utils/src/axios';

const VERSION_EFFECT_STATUS = {
	EFFECT: 1, // 1 = 生效
	UNEFFECT: 0 // 0 = 不生效
};

/* 根据表单版本id获取表单详情&处理级联选择数据 */
export default url => {
	return axios.get(url).then(data => {
		if (data.code === 0) {
			return data.data.map(item => {
				return {
					value: item.id,
					label: item.name,
					children: item.item.map(subItem => {
						return {
							value: subItem.versionId,
							label: subItem.versionNo,
							formList: subItem.formList,
							text: subItem.text, // 消息节点需要
							effectStatus: subItem.effectStatus, // 版本状态
							isEffect: subItem.effectStatus === VERSION_EFFECT_STATUS.EFFECT // 版本是否生效
						};
					})
				};
			});
		}
	});
};
